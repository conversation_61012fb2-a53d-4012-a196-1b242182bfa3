import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/translation/conversation_model.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/services/voice_translation/voice_translation_service.dart'
    hide sharedPreferencesProvider;
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/voice_translation/language_detection_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// A service for managing conversations
class ConversationService {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The voice translation service
  final VoiceTranslationService _translationService;

  /// The language pack service
  final LanguagePackService _languagePackService;

  /// The language detection service
  final LanguageDetectionService _languageDetectionService;

  /// The conversations
  List<ConversationModel> _conversations = [];

  /// The conversations stream controller
  final StreamController<List<ConversationModel>> _conversationsController =
      StreamController<List<ConversationModel>>.broadcast();

  /// The current conversation
  ConversationModel? _currentConversation;

  /// The current conversation stream controller
  final StreamController<ConversationModel?> _currentConversationController =
      StreamController<ConversationModel?>.broadcast();

  /// The continuous listening controller
  final StreamController<bool> _continuousListeningController =
      StreamController<bool>.broadcast();

  /// Whether continuous listening is active
  bool _isContinuousListeningActive = false;

  /// The continuous listening timer
  Timer? _continuousListeningTimer;

  /// The silence detection timer
  Timer? _silenceDetectionTimer;

  /// The conversation events controller
  final StreamController<String> _conversationEventsController =
      StreamController<String>.broadcast();

  /// Creates a new conversation service
  ConversationService(
    this._prefs,
    this._translationService,
    this._languagePackService,
    this._languageDetectionService,
  ) {
    _loadConversations();
  }

  /// Load conversations from shared preferences
  Future<void> _loadConversations() async {
    try {
      final conversationsJson = _prefs.getString('conversations');
      if (conversationsJson != null) {
        final List<dynamic> decoded = jsonDecode(conversationsJson);
        _conversations =
            decoded.map((item) => ConversationModel.fromJson(item)).toList();

        // Sort by updated date (newest first)
        _conversations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

        // Notify listeners
        _conversationsController.add(_conversations);
      }
    } catch (e) {
      debugPrint('Error loading conversations: $e');
    }
  }

  /// Save conversations to shared preferences
  Future<void> _saveConversations() async {
    try {
      final conversationsJson =
          jsonEncode(_conversations.map((item) => item.toJson()).toList());
      await _prefs.setString('conversations', conversationsJson);
    } catch (e) {
      debugPrint('Error saving conversations: $e');
    }
  }

  /// Start a new conversation
  Future<ConversationModel> startConversation({
    required String userLanguageCode,
    required String otherLanguageCode,
    String? title,
    bool autoDetectLanguage = false,
    bool speakerIdentification = false,
    ConversationSettings? settings,
    ConversationListeningMode listeningMode = ConversationListeningMode.manual,
  }) async {
    try {
      final now = DateTime.now();
      final id = const Uuid().v4();

      final conversation = ConversationModel(
        id: id,
        title: title ?? 'Conversation ${now.month}/${now.day}/${now.year}',
        userLanguageCode: userLanguageCode,
        otherLanguageCode: otherLanguageCode,
        turns: [],
        status: ConversationStatus.active,
        createdAt: now,
        updatedAt: now,
        autoDetectLanguage: autoDetectLanguage,
        speakerIdentification: speakerIdentification,
        listeningMode: listeningMode,
        settings: settings ?? const ConversationSettings(),
      );

      _conversations.insert(0, conversation);
      _conversationsController.add(_conversations);
      await _saveConversations();

      // Set as current conversation
      _currentConversation = conversation;
      _currentConversationController.add(_currentConversation);

      return conversation;
    } catch (e) {
      debugPrint('Error starting conversation: $e');
      rethrow;
    }
  }

  /// Add a turn to the conversation
  Future<ConversationTurn?> addConversationTurn({
    required String conversationId,
    required ConversationRole role,
    required String audioPath,
    required String sourceLanguageCode,
    required String targetLanguageCode,
  }) async {
    try {
      // Find the conversation
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return null;
      }

      final conversation = _conversations[index];

      // Create a translation
      final translation = await _translationService.startTranslation(
        sourceLanguage: sourceLanguageCode,
        targetLanguage: targetLanguageCode,
      );

      // Start recording
      await _translationService.startRecording(translation.id);

      // For demo purposes, we'll use a mock audio path
      // In a real app, this would be the actual audio path

      // Stop recording and translate
      final updatedTranslation =
          await _translationService.stopRecordingAndTranslate(translation.id);
      if (updatedTranslation == null) {
        return null;
      }

      // Create a turn
      final turnId = const Uuid().v4();
      final turn = ConversationTurn(
        id: turnId,
        role: role,
        translation: updatedTranslation,
        timestamp: DateTime.now(),
      );

      // Add the turn to the conversation
      final updatedConversation = conversation.addTurn(turn);
      _conversations[index] = updatedConversation;

      // Update current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
        _currentConversationController.add(_currentConversation);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return turn;
    } catch (e) {
      debugPrint('Error adding conversation turn: $e');
      return null;
    }
  }

  /// End a conversation
  Future<ConversationModel?> endConversation(String conversationId) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return null;
      }

      final conversation = _conversations[index];
      final updatedConversation = conversation.copyWith(
        status: ConversationStatus.ended,
        updatedAt: DateTime.now(),
      );

      _conversations[index] = updatedConversation;

      // Update current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
        _currentConversationController.add(_currentConversation);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return updatedConversation;
    } catch (e) {
      debugPrint('Error ending conversation: $e');
      return null;
    }
  }

  /// Pause a conversation
  Future<ConversationModel?> pauseConversation(String conversationId) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return null;
      }

      final conversation = _conversations[index];
      final updatedConversation = conversation.copyWith(
        status: ConversationStatus.paused,
        updatedAt: DateTime.now(),
      );

      _conversations[index] = updatedConversation;

      // Update current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
        _currentConversationController.add(_currentConversation);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return updatedConversation;
    } catch (e) {
      debugPrint('Error pausing conversation: $e');
      return null;
    }
  }

  /// Resume a conversation
  Future<ConversationModel?> resumeConversation(String conversationId) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return null;
      }

      final conversation = _conversations[index];
      final updatedConversation = conversation.copyWith(
        status: ConversationStatus.active,
        updatedAt: DateTime.now(),
      );

      _conversations[index] = updatedConversation;

      // Set as current conversation
      _currentConversation = updatedConversation;
      _currentConversationController.add(_currentConversation);

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return updatedConversation;
    } catch (e) {
      debugPrint('Error resuming conversation: $e');
      return null;
    }
  }

  /// Delete a conversation
  Future<bool> deleteConversation(String conversationId) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return false;
      }

      _conversationsemoveAt(index);

      // Clear current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = null;
        _currentConversationController.add(null);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return true;
    } catch (e) {
      debugPrint('Error deleting conversation: $e');
      return false;
    }
  }

  /// Toggle favorite status of a conversation
  Future<ConversationModel?> toggleFavorite(String conversationId) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return null;
      }

      final conversation = _conversations[index];
      final updatedConversation = conversation.copyWith(
        isFavorite: !conversation.isFavorite,
        updatedAt: DateTime.now(),
      );

      _conversations[index] = updatedConversation;

      // Update current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
        _currentConversationController.add(_currentConversation);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return updatedConversation;
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
      return null;
    }
  }

  /// Update conversation settings
  Future<ConversationModel?> updateConversationSettings({
    required String conversationId,
    String? title,
    String? userLanguageCode,
    String? otherLanguageCode,
    bool? autoDetectLanguage,
    bool? speakerIdentification,
    ConversationListeningMode? listeningMode,
    ConversationSettings? settings,
  }) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return null;
      }

      final conversation = _conversations[index];
      final updatedConversation = conversation.copyWith(
        title: title,
        userLanguageCode: userLanguageCode,
        otherLanguageCode: otherLanguageCode,
        autoDetectLanguage: autoDetectLanguage,
        speakerIdentification: speakerIdentification,
        listeningMode: listeningMode,
        settings: settings,
        updatedAt: DateTime.now(),
      );

      _conversations[index] = updatedConversation;

      // Update current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
        _currentConversationController.add(_currentConversation);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();

      return updatedConversation;
    } catch (e) {
      debugPrint('Error updating conversation settings: $e');
      return null;
    }
  }

  /// Get a conversation by ID
  ConversationModel? getConversation(String conversationId) {
    try {
      return _conversations.firstWhere((conv) => conv.id == conversationId);
    } catch (e) {
      return null;
    }
  }

  /// Set the current conversation
  void setCurrentConversation(String? conversationId) {
    if (conversationId == null) {
      _currentConversation = null;
      _currentConversationController.add(null);
      return;
    }

    try {
      _currentConversation =
          _conversations.firstWhere((conv) => conv.id == conversationId);
      _currentConversationController.add(_currentConversation);
    } catch (e) {
      debugPrint('Error setting current conversation: $e');
    }
  }

  /// Get active conversations
  List<ConversationModel> getActiveConversations() {
    return _conversations
        .where((conv) => conv.status == ConversationStatus.active)
        .toList();
  }

  /// Get favorite conversations
  List<ConversationModel> getFavoriteConversations() {
    return _conversations.where((conv) => conv.isFavorite).toList();
  }

  /// Get the conversations stream
  Stream<List<ConversationModel>> get conversationsStream =>
      _conversationsController.stream;

  /// Get the current conversation stream
  Stream<ConversationModel?> get currentConversationStream =>
      _currentConversationController.stream;

  /// Get all conversations
  List<ConversationModel> get conversations => _conversations;

  /// Get the current conversation
  ConversationModel? get currentConversation => _currentConversation;

  /// Start continuous listening mode
  Future<bool> startContinuousListening(String conversationId) async {
    try {
      final conversation = getConversation(conversationId);
      if (conversation == null ||
          conversation.status != ConversationStatus.active) {
        return false;
      }

      // Check if continuous listening is already active
      if (_isContinuousListeningActive) {
        return true;
      }

      _isContinuousListeningActive = true;
      _continuousListeningController.add(true);
      _conversationEventsController.add('Continuous listening started');

      // Start the continuous listening loop
      await _continuousListeningLoop(conversationId);

      return true;
    } catch (e) {
      debugPrint('Error starting continuous listening: $e');
      _isContinuousListeningActive = false;
      _continuousListeningController.add(false);
      return false;
    }
  }

  /// Stop continuous listening mode
  Future<bool> stopContinuousListening() async {
    try {
      if (!_isContinuousListeningActive) {
        return true;
      }

      _isContinuousListeningActive = false;
      _continuousListeningController.add(false);
      _conversationEventsController.add('Continuous listening stopped');

      // Cancel timers
      _continuousListeningTimer?.cancel();
      _silenceDetectionTimer?.cancel();

      return true;
    } catch (e) {
      debugPrint('Error stopping continuous listening: $e');
      return false;
    }
  }

  /// Continuous listening loop
  Future<void> _continuousListeningLoop(String conversationId) async {
    try {
      final conversation = getConversation(conversationId);
      if (conversation == null ||
          conversation.status != ConversationStatus.active) {
        await stopContinuousListening();
        return;
      }

      // Get the current role
      final currentRole = conversation.lastActiveRole;

      // Get the source and target language codes based on the role
      final sourceLanguageCode = currentRole == ConversationRole.user
          ? conversation.userLanguageCode
          : conversation.otherLanguageCode;

      final targetLanguageCode = currentRole == ConversationRole.user
          ? conversation.otherLanguageCode
          : conversation.userLanguageCode;

      // Start a translation
      final translation = await _translationService.startTranslation(
        sourceLanguage: sourceLanguageCode,
        targetLanguage: targetLanguageCode,
      );

      // Start recording
      await _translationService.startRecording(translation.id);

      // Set up silence detection timer
      final silenceDurationMs = conversation.settings.silenceDurationMs;
      _silenceDetectionTimer =
          Timer(Duration(milliseconds: silenceDurationMs), () async {
        // Stop recording and translate
        final updatedTranslation =
            await _translationService.stopRecordingAndTranslate(translation.id);
        if (updatedTranslation != null) {
          // Check if there's actual content (to avoid empty turns)
          if (updatedTranslation.originalText != null &&
              updatedTranslation.originalText!.isNotEmpty) {
            // Create a turn
            final turnId = const Uuid().v4();
            final turn = ConversationTurn(
              id: turnId,
              role: currentRole,
              translation: updatedTranslation,
              timestamp: DateTime.now(),
            );

            // Add the turn to the conversation
            await _addTurnToConversation(conversationId, turn);

            // If auto-switch speakers is enabled, switch to the next role
            if (conversation.settings.autoSwitchSpeakers) {
              final nextRole = conversation.getNextRole(currentRole);
              final updatedConversation = getConversation(conversationId);
              if (updatedConversation != null) {
                final newConversation = updatedConversation.copyWith(
                  lastActiveRole: nextRole,
                  updatedAt: DateTime.now(),
                );

                final index = _conversations
                    .indexWhere((conv) => conv.id == conversationId);
                if (index >= 0) {
                  _conversations[index] = newConversation;

                  // Update current conversation if needed
                  if (_currentConversation?.id == conversationId) {
                    _currentConversation = newConversation;
                    _currentConversationController.add(_currentConversation);
                  }

                  // Notify listeners
                  _conversationsController.add(_conversations);
                  await _saveConversations();
                }
              }
            }
          }
        }

        // Continue the loop if still active
        if (_isContinuousListeningActive) {
          _continuousListeningTimer =
              Timer(const Duration(milliseconds: 500), () {
            _continuousListeningLoop(conversationId);
          });
        }
      });
    } catch (e) {
      debugPrint('Error in continuous listening loop: $e');
      await stopContinuousListening();
    }
  }

  /// Add a turn to the conversation (internal method)
  Future<void> _addTurnToConversation(
      String conversationId, ConversationTurn turn) async {
    try {
      final index =
          _conversations.indexWhere((conv) => conv.id == conversationId);
      if (index < 0) {
        return;
      }

      final conversation = _conversations[index];
      final updatedConversation = conversation.addTurn(turn);
      _conversations[index] = updatedConversation;

      // Update current conversation if needed
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
        _currentConversationController.add(_currentConversation);
      }

      // Notify listeners
      _conversationsController.add(_conversations);
      await _saveConversations();
    } catch (e) {
      debugPrint('Error adding turn to conversation: $e');
    }
  }

  /// Export a conversation
  Future<String?> exportConversation(
    String conversationId,
    ConversationExportFormat format,
  ) async {
    try {
      final conversation = getConversation(conversationId);
      if (conversation == null) {
        return null;
      }

      // Check if the conversation can be exported
      if (!conversation.canExport) {
        return null;
      }

      String content;
      String extension;

      // Generate content based on format
      switch (format) {
        case ConversationExportFormat.text:
          content = conversation.exportToText();
          extension = 'txt';
          break;
        case ConversationExportFormat.json:
          content = conversation.exportToJson();
          extension = 'json';
          break;
        case ConversationExportFormat.pdf:
          // PDF export would require a PDF generation library
          // For now, we'll just use text format
          content = conversation.exportToText();
          extension = 'txt';
          break;
      }

      // Save to a file
      final directory = await getTemporaryDirectory();
      final fileName =
          '${conversation.titleeplaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.$extension';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await fileriteAsString(content);

      _conversationEventsController.add('Conversation exported to $filePath');

      return filePath;
    } catch (e) {
      debugPrint('Error exporting conversation: $e');
      return null;
    }
  }

  /// Search conversations
  List<ConversationModel> searchConversations(String query) {
    if (query.isEmpty) {
      return _conversations;
    }

    final lowercaseQuery = query.toLowerCase();

    return _conversations.where((conversation) {
      // Search in title
      if (conversation.title.toLowerCase().contains(lowercaseQuery)) {
        return true;
      }

      // Search in turns
      for (final turn in conversation.turns) {
        if (turn.translation.originalText != null &&
            turn.translation.originalText!
                .toLowerCase()
                .contains(lowercaseQuery)) {
          return true;
        }

        if (turn.translation.translatedText != null &&
            turn.translation.translatedText!
                .toLowerCase()
                .contains(lowercaseQuery)) {
          return true;
        }
      }

      return false;
    }).toList();
  }

  /// Filter conversations by date range
  List<ConversationModel> filterConversationsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return _conversationshere((conversation) {
      final date = conversation.createdAt;
      return date.isAfter(startDate) &&
          date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// Get the continuous listening stream
  Stream<bool> get continuousListeningStream =>
      _continuousListeningController.stream;

  /// Get the conversation events stream
  Stream<String> get conversationEventsStream =>
      _conversationEventsController.stream;

  /// Check if continuous listening is active
  bool get isContinuousListeningActive => _isContinuousListeningActive;

  /// Dispose the service
  Future<void> dispose() async {
    // Stop continuous listening if active
    if (_isContinuousListeningActive) {
      await stopContinuousListening();
    }

    await _conversationsController.close();
    await _currentConversationController.close();
    await _continuousListeningController.close();
    await _conversationEventsController.close();
  }
}

/// Provider for the conversation service
final conversationServiceProvider = Provider<ConversationService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final translationService = ref.watch(voiceTranslationServiceProvider);
  final languagePackService = ref.watch(languagePackServiceProvider);
  final languageDetectionService = ref.watch(languageDetectionServiceProvider);

  final service = ConversationService(
    prefs,
    translationService,
    languagePackService,
    languageDetectionService,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
