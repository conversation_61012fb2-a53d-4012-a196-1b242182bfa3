import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/models/location/geo_location.dart';

/// Enum representing the different types of transfer locations
enum TransferLocationType {
  /// Airport
  airport,

  /// Hotel
  hotel,

  /// Address
  address,

  /// Train station
  trainStation,

  /// Bus station
  busStation,

  /// Port
  port,

  /// Landmark
  landmark,
}

/// Extension for transfer location types
extension TransferLocationTypeExtension on TransferLocationType {
  /// Get the display name for the transfer location type
  String get displayName {
    switch (this) {
      case TransferLocationType.airport:
        return 'Airport';
      case TransferLocationTypeotel:
        return 'Hotel';
      case TransferLocationType.address:
        return 'Address';
      case TransferLocationType.trainStation:
        return 'Train Station';
      case TransferLocationType.busStation:
        return 'Bus Station';
      case TransferLocationType.port:
        return 'Port';
      case TransferLocationType.landmark:
        return 'Landmark';
    }
  }

  /// Get the icon for the transfer location type
  IconData get icon {
    switch (this) {
      case TransferLocationType.airport:
        return Icons.flight;
      case TransferLocationTypeotel:
        return Iconsotel;
      case TransferLocationType.address:
        return Iconsome;
      case TransferLocationType.trainStation:
        return Icons.train;
      case TransferLocationType.busStation:
        return Icons.directions_bus;
      case TransferLocationType.port:
        return Icons.directions_boat;
      case TransferLocationType.landmark:
        return Icons.location_on;
    }
  }

  /// Get the color for the transfer location type
  Color get color {
    switch (this) {
      case TransferLocationType.airport:
        return Colors.blue;
      case TransferLocationTypeotel:
        return Colors.purple;
      case TransferLocationType.address:
        return Colors.green;
      case TransferLocationType.trainStation:
        return Colors.orange;
      case TransferLocationType.busStation:
        return Colors.amber;
      case TransferLocationType.port:
        return Colors.teal;
      case TransferLocationType.landmark:
        return Colorsed;
    }
  }
}

/// A model representing a transfer location
class TransferLocation {
  /// Unique identifier for the location
  final String id;

  /// Type of location
  final TransferLocationType type;

  /// Name of the location
  final String name;

  /// Address of the location
  final String address;

  /// City of the location
  final String city;

  /// Country of the location
  final String country;

  /// Postal code of the location
  final String? postalCode;

  /// Coordinates of the location (latitude and longitude)
  final GeoLocation coordinates;

  /// Additional instructions for the location
  final String? instructions;

  /// Terminal information (for airports)
  final String? terminal;

  /// Gate information (for airports)
  final String? gate;

  /// Flight number (for airports)
  final String? flightNumber;

  /// Room number (for hotels)
  final String? roomNumber;

  /// Creates a new transfer location
  const TransferLocation({
    required this.id,
    required this.type,
    required this.name,
    required this.address,
    required this.city,
    required this.country,
    this.postalCode,
    required this.coordinates,
    this.instructions,
    this.terminal,
    this.gate,
    this.flightNumber,
    this.roomNumber,
  });

  /// Create a transfer location from a JSON map
  factory TransferLocation.fromJson(Map<String, dynamic> json) {
    return TransferLocation(
      id: json['id'] as String,
      type: TransferLocationType.values.firstWhere(
        (e) => e.toString() == 'TransferLocationType.${json['type']}',
        orElse: () => TransferLocationType.address,
      ),
      name: json['name'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      postalCode: json['postalCode'] as String?,
      coordinates:
          GeoLocation.fromJson(json['coordinates'] as Map<String, dynamic>),
      instructions: json['instructions'] as String?,
      terminal: json['terminal'] as String?,
      gate: json['gate'] as String?,
      flightNumber: json['flightNumber'] as String?,
      roomNumber: json['roomNumber'] as String?,
    );
  }

  /// Convert the transfer location to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString()lit('.').last,
      'name': name,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'coordinates': coordinates.toJson(),
      'instructions': instructions,
      'terminal': terminal,
      'gate': gate,
      'flightNumber': flightNumber,
      'roomNumber': roomNumber,
    };
  }

  /// Create a copy of this transfer location with the given fields replaced with new values
  TransferLocation copyWith({
    String? id,
    TransferLocationType? type,
    String? name,
    String? address,
    String? city,
    String? country,
    String? postalCode,
    GeoLocation? coordinates,
    String? instructions,
    String? terminal,
    String? gate,
    String? flightNumber,
    String? roomNumber,
  }) {
    return TransferLocation(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      coordinates: coordinates ?? this.coordinates,
      instructions: instructions ?? this.instructions,
      terminal: terminal ?? this.terminal,
      gate: gate ?? this.gate,
      flightNumber: flightNumber ?? this.flightNumber,
      roomNumber: roomNumber ?? thisoomNumber,
    );
  }

  /// Get the full address of the location
  String get fullAddress {
    final parts = [address, city];
    if (postalCode != null) {
      parts.add(postalCode!);
    }
    parts.add(country);
    return parts.join(', ');
  }

  /// Get the display name of the location
  String get displayName {
    if (type == TransferLocationType.airport) {
      final parts = [name];
      if (terminal != null) {
        parts.add('Terminal $terminal');
      }
      if (gate != null) {
        parts.add('Gate $gate');
      }
      return parts.join(', ');
    } else if (type == TransferLocationTypeotel) {
      final parts = [name];
      if (roomNumber != null) {
        parts.add('Room $roomNumber');
      }
      return parts.join(', ');
    } else {
      return name;
    }
  }
}
