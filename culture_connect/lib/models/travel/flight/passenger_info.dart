import 'package:culture_connect/models/travel/flight_search_params.dart';

/// Enum for seat preferences
enum SeatPreference {
  /// No preference
  none,

  /// Window seat
  window,

  /// Aisle seat
  aisle,

  /// Exit row seat
  exitRow,

  /// Front of aircraft
  front,

  /// Rear of aircraft
  rear,
}

/// Extension for seat preferences
extension SeatPreferenceExtension on SeatPreference {
  /// Get the display name for the seat preference
  String get displayName {
    switch (this) {
      case SeatPreference.none:
        return 'No Preference';
      case SeatPreferenceindow:
        return 'Window';
      case SeatPreference.aisle:
        return 'Aisle';
      case SeatPreference.exitRow:
        return 'Exit Row';
      case SeatPreference.front:
        return 'Front of Aircraft';
      case SeatPreferenceear:
        return 'Rear of Aircraft';
    }
  }
}

/// Enum for meal preferences
enum MealPreference {
  /// No preference
  none,

  /// Regular meal
  regular,

  /// Vegetarian meal
  vegetarian,

  /// Vegan meal
  vegan,

  /// Kosher meal
  kosher,

  /// Halal meal
  halal,

  /// Gluten-free meal
  glutenFree,

  /// Diabetic meal
  diabetic,

  /// Low-calorie meal
  lowCalorie,

  /// Low-sodium meal
  lowSodium,

  /// Lactose-free meal
  lactoseFree,

  /// Seafood meal
  seafood,

  /// Hindu meal
  hindu,

  /// Child meal
  child,

  /// Infant meal
  infant,
}

/// Extension for meal preferences
extension MealPreferenceExtension on MealPreference {
  /// Get the display name for the meal preference
  String get displayName {
    switch (this) {
      case MealPreference.none:
        return 'No Preference';
      case MealPreferenceegular:
        return 'Regular Meal';
      case MealPreference.vegetarian:
        return 'Vegetarian Meal';
      case MealPreference.vegan:
        return 'Vegan Meal';
      case MealPreference.kosher:
        return 'Kosher Meal';
      case MealPreferencealal:
        return 'Halal Meal';
      case MealPreference.glutenFree:
        return 'Gluten-Free Meal';
      case MealPreference.diabetic:
        return 'Diabetic Meal';
      case MealPreference.lowCalorie:
        return 'Low-Calorie Meal';
      case MealPreference.lowSodium:
        return 'Low-Sodium Meal';
      case MealPreference.lactoseFree:
        return 'Lactose-Free Meal';
      case MealPreference.seafood:
        return 'Seafood Meal';
      case MealPreferenceindu:
        return 'Hindu Meal';
      case MealPreference.child:
        return 'Child Meal';
      case MealPreference.infant:
        return 'Infant Meal';
    }
  }
}

/// A model representing passenger information for a flight booking
class PassengerInfo {
  /// Type of passenger
  final PassengerType type;

  /// First name of the passenger
  final String firstName;

  /// Last name of the passenger
  final String lastName;

  /// Date of birth of the passenger
  final DateTime? dateOfBirth;

  /// Gender of the passenger
  final String? gender;

  /// Passport number of the passenger
  final String passportNumber;

  /// Passport expiry date of the passenger
  final DateTime? passportExpiryDate;

  /// Nationality of the passenger
  final String nationality;

  /// Whether the passenger requires special assistance
  final bool specialAssistance;

  /// Details of the special assistance required
  final String specialAssistanceDetails;

  /// Seat preference of the passenger
  final SeatPreference seatPreference;

  /// Meal preference of the passenger
  final MealPreference mealPreference;

  /// Creates a new passenger information
  const PassengerInfo({
    required this.type,
    required this.firstName,
    required this.lastName,
    this.dateOfBirth,
    this.gender,
    required this.passportNumber,
    this.passportExpiryDate,
    required this.nationality,
    required this.specialAssistance,
    required this.specialAssistanceDetails,
    required this.seatPreference,
    required this.mealPreference,
  });

  /// Creates a copy of this passenger information with the given fields replaced
  PassengerInfo copyWith({
    PassengerType? type,
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    String? gender,
    String? passportNumber,
    DateTime? passportExpiryDate,
    String? nationality,
    bool? specialAssistance,
    String? specialAssistanceDetails,
    SeatPreference? seatPreference,
    MealPreference? mealPreference,
  }) {
    return PassengerInfo(
      type: type ?? this.type,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      passportNumber: passportNumber ?? this.passportNumber,
      passportExpiryDate: passportExpiryDate ?? this.passportExpiryDate,
      nationality: nationality ?? this.nationality,
      specialAssistance: specialAssistance ?? this.specialAssistance,
      specialAssistanceDetails:
          specialAssistanceDetails ?? this.specialAssistanceDetails,
      seatPreference: seatPreference ?? this.seatPreference,
      mealPreference: mealPreference ?? this.mealPreference,
    );
  }

  /// Get the full name of the passenger
  String get fullName => '$firstName $lastName';

  /// Get the formatted date of birth
  String? get formattedDateOfBirth {
    if (dateOfBirth == null) return null;
    return '${dateOfBirth!.year}-${dateOfBirth!.month.toString().padLeft(2, '0')}-${dateOfBirth!.day.toString().padLeft(2, '0')}';
  }

  /// Get the formatted passport expiry date
  String? get formattedPassportExpiryDate {
    if (passportExpiryDate == null) return null;
    return '${passportExpiryDate!.year}-${passportExpiryDate!.month.toString().padLeft(2, '0')}-${passportExpiryDate!.day.toString().padLeft(2, '0')}';
  }

  /// Check if the passenger information is complete
  bool get isComplete {
    return firstName.isNotEmpty &&
        lastName.isNotEmpty &&
        dateOfBirth != null &&
        gender != null &&
        passportNumber.isNotEmpty &&
        passportExpiryDate != null &&
        nationality.isNotEmpty;
  }

  /// Creates a passenger information from JSON
  factory PassengerInfo.fromJson(Map<String, dynamic> json) {
    return PassengerInfo(
      type: _parsePassengerType(json['type'] as String),
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      gender: json['gender'] as String?,
      passportNumber: json['passportNumber'] as String,
      passportExpiryDate: json['passportExpiryDate'] != null
          ? DateTime.parse(json['passportExpiryDate'] as String)
          : null,
      nationality: json['nationality'] as String,
      specialAssistance: json['specialAssistance'] as bool,
      specialAssistanceDetails:
          json['specialAssistanceDetails'] as String? ?? '',
      seatPreference: _parseSeatPreference(json['seatPreference'] as String?),
      mealPreference: _parseMealPreference(json['mealPreference'] as String?),
    );
  }

  /// Converts this passenger information to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'firstName': firstName,
      'lastName': lastName,
      if (dateOfBirth != null) 'dateOfBirth': dateOfBirth!.toIso8601String(),
      if (gender != null) 'gender': gender,
      'passportNumber': passportNumber,
      if (passportExpiryDate != null)
        'passportExpiryDate': passportExpiryDate!.toIso8601String(),
      'nationality': nationality,
      'specialAssistance': specialAssistance,
      'specialAssistanceDetails': specialAssistanceDetails,
      'seatPreference': seatPreference.name,
      'mealPreference': mealPreference.name,
    };
  }

  /// Parse passenger type from string
  static PassengerType _parsePassengerType(String value) {
    return PassengerType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => PassengerType.adult,
    );
  }

  /// Parse seat preference from string
  static SeatPreference _parseSeatPreference(String? value) {
    if (value == null) return SeatPreference.none;
    return SeatPreference.values.firstWhere(
      (pref) => pref.name == value,
      orElse: () => SeatPreference.none,
    );
  }

  /// Parse meal preference from string
  static MealPreference _parseMealPreference(String? value) {
    if (value == null) return MealPreference.none;
    return MealPreference.values.firstWhere(
      (pref) => pref.name == value,
      orElse: () => MealPreference.none,
    );
  }
}
