import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/payment/payment_method.dart';
import 'package:culture_connect/models/payment/transaction.dart';
import 'package:culture_connect/models/receipt.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/screens/payment/receipt_screen.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// Provider for getting a receipt by transaction ID
final receiptProvider =
    FutureProvider.family<Receipt?, String>((ref, transactionId) async {
  final paymentService = PaymentService();
  return await paymentService.getReceipt(transactionId);
});

/// Provider for getting an experience by ID
final experienceByIdProvider =
    FutureProvider.family<Experience?, String>((ref, experienceId) async {
  final experiencesAsync = ref.watch(experiencesProvider);
  return experiencesAsync.when(
    data: (experiences) {
      try {
        return experiences.firstWhere(
          (exp) => exp.id == experienceId,
        );
      } catch (e) {
        return null;
      }
    },
    loading: () => null,
    error: (_, __) => null,
  );
});

/// Screen for displaying transaction details
class TransactionDetailsScreen extends ConsumerWidget {
  /// The transaction to display
  final Transaction transaction;

  /// Creates a new transaction details screen
  const TransactionDetailsScreen({
    super.key,
    required this.transaction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final experienceAsync =
        ref.watch(experienceByIdProvider(transaction.experienceId));
    final receiptAsync = ref.watch(receiptProvider(transaction.id));

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Transaction Details',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'Help',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction status
              _buildTransactionStatus(),
              const SizedBox(height: 24),

              // Transaction details
              _buildTransactionDetails(),
              const SizedBox(height: 24),

              // Experience details
              experienceAsync.when(
                data: (experience) {
                  if (experience != null) {
                    return _buildExperienceDetails(experience);
                  }
                  return const SizedBox.shrink();
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (_, __) => const SizedBox.shrink(),
              ),
              const SizedBox(height: 24),

              // Payment method
              _buildPaymentMethod(),
              const SizedBox(height: 24),

              // Receipt button
              receiptAsync.when(
                data: (receipt) {
                  if (receipt != null) {
                    return _buildReceiptButton(context, receipt);
                  }
                  return const SizedBox.shrink();
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (_, __) => const SizedBox.shrink(),
              ),
              const SizedBox(height: 24),

              // Support section
              _buildSupportSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionStatus() {
    // Determine status color and icon
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (transaction.status) {
      case TransactionStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'Payment Completed';
        break;
      case TransactionStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.access_time;
        statusText = 'Payment Pending';
        break;
      case TransactionStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = 'Payment Failed';
        break;
      case TransactionStatus.refunded:
        statusColor = Colors.purple;
        statusIcon = Icons.replay;
        statusText = 'Payment Refunded';
        break;
      case TransactionStatus.processing:
        statusColor = Colors.blue;
        statusIcon = Icons.sync;
        statusText = 'Payment Processing';
        break;
      case TransactionStatus.partiallyRefunded:
        statusColor = Colors.purple;
        statusIcon = Icons.replay_circle_filled;
        statusText = 'Partially Refunded';
        break;
      case TransactionStatus.disputed:
        statusColor = Colors.red;
        statusIcon = Icons.gavel;
        statusText = 'Payment Disputed';
        break;
      case TransactionStatus.canceled:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        statusText = 'Payment Canceled';
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withAlpha(26), // 0.1 opacity
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withAlpha(77)), // 0.3 opacity
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                statusText,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: statusColor,
                ),
              ),
              if (transaction.status == TransactionStatus.failed &&
                  transaction.errorMessage != null) ...[
                const SizedBox(height: 4),
                Text(
                  transaction.errorMessage!,
                  style: TextStyle(
                    fontSize: 12,
                    color: statusColor,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Transaction Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Transaction ID
          _buildDetailRow(
            'Transaction ID',
            transaction.id,
            canCopy: true,
          ),
          const SizedBox(height: 12),

          // Date and time
          _buildDetailRow(
            'Date & Time',
            DateFormat('MMM d, yyyy • h:mm a').format(transaction.createdAt),
          ),
          const SizedBox(height: 12),

          // Amount
          _buildDetailRow(
            'Amount',
            transaction.getFormattedAmount(),
            valueColor:
                transaction.paymentMethodType == PaymentMethodType.creditCard
                    ? Colors.red
                    : Colors.green,
            valueFontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 12),

          // Transaction type
          _buildDetailRow(
            'Type',
            _capitalizeFirst(transaction.paymentMethodType.displayName),
          ),
          const SizedBox(height: 12),

          // Reference number
          if (transaction.processorTransactionId != null) ...[
            _buildDetailRow(
              'Reference Number',
              transaction.processorTransactionId!,
              canCopy: true,
            ),
            const SizedBox(height: 12),
          ],
        ],
      ),
    );
  }

  Widget _buildExperienceDetails(Experience experience) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Experience Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Experience info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Experience image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  experience.imageUrl,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),

              // Experience details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      experience.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      experience.location,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          size: 16,
                          color: Colors.amber,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${experience.rating} (${experience.reviewCount} reviews)',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethod() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Method',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Payment method info
          Row(
            children: [
              // Payment method icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withAlpha(26), // 0.1 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getPaymentMethodIcon(transaction.paymentMethodType),
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // Payment method details
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.paymentMethodType.displayName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptButton(BuildContext context, Receipt receipt) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ReceiptScreen(
                receipt: receipt,
                experience: Experience(
                  id: transaction.experienceId,
                  title: transaction.description,
                  description: '',
                  imageUrl: '',
                  category: '',
                  rating: 0,
                  reviewCount: 0,
                  price: transaction.amount,
                  location: '',
                  coordinates: const LatLng(0, 0),
                  guideId: '',
                  guideName: '',
                  guideImageUrl: '',
                  languages: const [],
                  includedItems: const [],
                  requirements: const [],
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                  durationHours: 0,
                ),
                booking: Booking(
                  id: transaction.bookingId,
                  experienceId: transaction.experienceId,
                  date: transaction.createdAt,
                  participantCount: 1,
                  totalAmount: transaction.amount,
                  status: BookingStatus.confirmed,
                  createdAt: transaction.createdAt,
                  updatedAt: transaction.createdAt,
                  timeSlot: TimeSlot(
                    startTime: transaction.createdAt,
                    endTime:
                        transaction.createdAt.add(const Duration(hours: 2)),
                  ),
                  // specialRequirements: '', // Removed if not in constructor
                ),
              ),
            ),
          );
        },
        icon: const Icon(Icons.receipt),
        label: const Text('View Receipt'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Need Help?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'If you have any questions about this transaction or need assistance, please contact our support team.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // Contact support action
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Contacting support...'),
                  ),
                );
              },
              icon: const Icon(Icons.support_agent),
              label: const Text('Contact Support'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: const BorderSide(color: AppTheme.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool canCopy = false,
    Color? valueColor,
    FontWeight? valueFontWeight,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade700,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: valueColor ?? Colors.black,
                fontWeight: valueFontWeight ?? FontWeight.normal,
              ),
            ),
            if (canCopy) ...[
              const SizedBox(width: 4),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: value));
                },
                child: const Icon(
                  Icons.copy,
                  size: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Help'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Understanding Your Transaction',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This screen shows the details of your transaction, including the status, amount, and payment method used.',
              style: TextStyle(
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Transaction Statuses:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildStatusHelp('Completed', 'Payment was successful'),
            const SizedBox(height: 4),
            _buildStatusHelp('Pending', 'Payment is being processed'),
            const SizedBox(height: 4),
            _buildStatusHelp(
                'Processing', 'Payment is being processed by the provider'),
            const SizedBox(height: 4),
            _buildStatusHelp('Failed', 'Payment was not successful'),
            const SizedBox(height: 4),
            _buildStatusHelp('Refunded', 'Payment was fully refunded'),
            const SizedBox(height: 4),
            _buildStatusHelp(
                'Partially Refunded', 'Payment was partially refunded'),
            const SizedBox(height: 4),
            _buildStatusHelp('Disputed', 'Payment is under dispute'),
            const SizedBox(height: 4),
            _buildStatusHelp('Canceled', 'Payment was canceled'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusHelp(String status, String description) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getStatusColor(status),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$status: ',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: Text(
            description,
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      case 'refunded':
        return Colors.purple;
      case 'processing':
        return Colors.blue;
      case 'partially refunded':
        return Colors.purple;
      case 'disputed':
        return Colors.red;
      case 'canceled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethodType type) {
    return switch (type) {
      PaymentMethodType.creditCard => Icons.credit_card,
      PaymentMethodType.paypal => Icons.payment,
      PaymentMethodType.applePay => Icons.apple,
      PaymentMethodType.googlePay => Icons.g_mobiledata,
      PaymentMethodType.bitcoin => Icons.currency_bitcoin,
      PaymentMethodType.ethereum => Icons.currency_exchange,
      PaymentMethodType.bankTransfer => Icons.account_balance,
    };
  }

  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}
