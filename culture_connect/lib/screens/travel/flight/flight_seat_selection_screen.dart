// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/travel/flight_search_service.dart';
import 'package:culture_connect/providers/travel/flight_providers.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/models/travel/flight_search_params.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// Screen for selecting seats for flight booking
class FlightSeatSelectionScreen extends ConsumerStatefulWidget {
  /// Creates a new flight seat selection screen
  const FlightSeatSelectionScreen({super.key});

  @override
  ConsumerState<FlightSeatSelectionScreen> createState() =>
      _FlightSeatSelectionScreenState();
}

class _FlightSeatSelectionScreenState
    extends ConsumerState<FlightSeatSelectionScreen> {
  // State variables
  SeatMap? _seatMap;
  bool _isLoading = true;
  String? _errorMessage;
  int _currentPassengerIndex = 0;
  SeatClass? _selectedSeatClass;
  SeatType? _selectedSeatType;
  bool _showOnlyAvailable = true;

  @override
  void initState() {
    super.initState();
    _loadSeatMap();
  }

  Future<void> _loadSeatMap() async {
    try {
      setState(() => _isLoading = true);

      // In a real app, this would get the flight ID from the selected flight
      // For now, using a mock flight ID
      const flightId = 'mock-flight-123';

      final flightService = FlightSearchService();
      final seatMap = await flightService.getSeatMap(flightId);

      if (mounted) {
        setState(() {
          _seatMap = seatMap;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load seat map: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final passengerInfo = ref.watch(passengerInfoProvider);
    final selectedSeats = ref.watch(selectedSeatsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Select Seats (${_currentPassengerIndex + 1}/${passengerInfo.length})'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            onPressed: _showFiltersDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter seats',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorView(
                    error: _errorMessage!,
                    onRetry: _loadSeatMap,
                  ),
                )
              : _seatMap == null
                  ? const Center(child: Text('No seat map available'))
                  : _buildSeatSelectionContent(
                      theme, passengerInfo, selectedSeats),
      bottomNavigationBar:
          _buildNavigationBar(theme, passengerInfo, selectedSeats),
    );
  }

  Widget _buildSeatSelectionContent(
    ThemeData theme,
    List<PassengerInfo> passengerInfo,
    Map<String, String> selectedSeats,
  ) {
    return Column(
      children: [
        _buildPassengerSelector(theme, passengerInfo),
        _buildSeatLegend(theme),
        Expanded(
          child: _buildSeatMap(theme, selectedSeats),
        ),
        _buildSeatInfo(theme, selectedSeats),
      ],
    );
  }

  Widget _buildPassengerSelector(
      ThemeData theme, List<PassengerInfo> passengerInfo) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select seat for:',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: passengerInfo.length,
              itemBuilder: (context, index) {
                final passenger = passengerInfo[index];
                final isSelected = _currentPassengerIndex == index;

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(passenger.firstName),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() => _currentPassengerIndex = index);
                      }
                    },
                    avatar: Icon(
                      _getPassengerTypeIcon(passenger.type),
                      size: 16,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeatLegend(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildLegendItem(theme, Colors.green, 'Available'),
          _buildLegendItem(theme, Colors.red, 'Occupied'),
          _buildLegendItem(theme, theme.colorScheme.primary, 'Selected'),
          _buildLegendItem(theme, Colors.orange, 'Premium'),
        ],
      ),
    );
  }

  Widget _buildLegendItem(ThemeData theme, Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: theme.colorScheme.outline.withAlpha(77)),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildSeatMap(ThemeData theme, Map<String, String> selectedSeats) {
    if (_seatMap == null) return const SizedBox.shrink();

    final filteredSeats = _getFilteredSeats();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Aircraft front indicator
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '✈️ Front of Aircraft',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Seat grid
          ...List.generate(_seatMap!.rows, (rowIndex) {
            final row = rowIndex + 1;
            final rowSeats =
                filteredSeats.where((seat) => seat.row == row).toList();

            if (rowSeats.isEmpty && _showOnlyAvailable) {
              return const SizedBox.shrink();
            }

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Row number
                  SizedBox(
                    width: 30,
                    child: Text(
                      row.toString(),
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodySmall,
                    ),
                  ),

                  // Seats
                  ...List.generate(_seatMap!.columns, (colIndex) {
                    final seat = rowSeats.firstWhere(
                      (s) => s.column == colIndex,
                      orElse: () => Seat(
                        id: '',
                        thisow: row,
                        column: colIndex,
                        seatClass: SeatClass.economy,
                        seatType: SeatType.middle,
                        isAvailable: false,
                        isExitRow: false,
                        price: 0,
                      ),
                    );

                    if (seat.id.isEmpty) {
                      return const SizedBox(width: 40, height: 40);
                    }

                    return _buildSeatWidget(theme, seat, selectedSeats);
                  }),

                  // Aisle space
                  if (_seatMap!.columns > 3) const SizedBox(width: 20),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSeatWidget(
      ThemeData theme, Seat seat, Map<String, String> selectedSeats) {
    final currentPassenger =
        ref.read(passengerInfoProvider)[_currentPassengerIndex];
    final isSelected = selectedSeats[currentPassenger.firstName] == seat.id;
    final isOccupiedByOther =
        selectedSeats.values.contains(seat.id) && !isSelected;

    Color seatColor;
    if (isSelected) {
      seatColor = theme.colorScheme.primary;
    } else if (!seat.isAvailable || isOccupiedByOther) {
      seatColor = Colors.red;
    } else if (seat.price > 0) {
      seatColor = Colors.orange;
    } else {
      seatColor = Colors.green;
    }

    return Padding(
      padding: const EdgeInsets.all(2),
      child: GestureDetector(
        onTap: () => _selectSeat(seat),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: seatColor,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(77),
              width: 1,
            ),
          ),
          child: Center(
            child: Text(
              seat.id,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 10,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSeatInfo(ThemeData theme, Map<String, String> selectedSeats) {
    final currentPassenger =
        ref.read(passengerInfoProvider)[_currentPassengerIndex];
    final selectedSeatId = selectedSeats[currentPassenger.firstName];

    if (selectedSeatId == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Select a seat for ${currentPassenger.firstName}',
          style: theme.textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
      );
    }

    final selectedSeat =
        _seatMap?.seats.firstWhere((seat) => seat.id == selectedSeatId);
    if (selectedSeat == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Selected Seat: ${selectedSeat.id}',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Type: ${_getSeatTypeLabel(selectedSeat.seatType)}'),
                    Text(
                        'Class: ${_getSeatClassLabel(selectedSeat.seatClass)}'),
                    if (selectedSeat.isExitRow) const Text('Exit Row'),
                  ],
                ),
              ),
              if (selectedSeat.price > 0)
                Text(
                  '+\$${selectedSeat.price.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationBar(
    ThemeData theme,
    List<PassengerInfo> passengerInfo,
    Map<String, String> selectedSeats,
  ) {
    final allSeatsSelected = passengerInfo.every(
      (passenger) => selectedSeats.containsKey(passenger.firstName),
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _skipSeatSelection,
              child: const Text('Skip Seat Selection'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed:
                  allSeatsSelected ? _proceedToBookingConfirmation : null,
              child: const Text('Continue to Booking'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  List<Seat> _getFilteredSeats() {
    if (_seatMap == null) return [];

    var seats = _seatMap!.seats;

    if (_showOnlyAvailable) {
      seats = seats.where((seat) => seat.isAvailable).toList();
    }

    if (_selectedSeatClass != null) {
      seats =
          seats.where((seat) => seat.seatClass == _selectedSeatClass).toList();
    }

    if (_selectedSeatType != null) {
      seats =
          seats.where((seat) => seat.seatType == _selectedSeatType).toList();
    }

    return seats;
  }

  IconData _getPassengerTypeIcon(PassengerType type) {
    switch (type) {
      case PassengerType.adult:
        return Icons.person;
      case PassengerType.child:
        return Icons.child_care;
      case PassengerType.infant:
        return Icons.baby_changing_station;
    }
  }

  String _getSeatTypeLabel(SeatType type) {
    switch (type) {
      case SeatType.window:
        return 'Window';
      case SeatType.middle:
        return 'Middle';
      case SeatType.aisle:
        return 'Aisle';
      case SeatType.exit:
        return 'Exit Row';
    }
  }

  String _getSeatClassLabel(SeatClass seatClass) {
    switch (seatClass) {
      case SeatClass.economy:
        return 'Economy';
      case SeatClass.premiumEconomy:
        return 'Premium Economy';
      case SeatClass.business:
        return 'Business';
      case SeatClass.first:
        return 'First';
    }
  }

  void _selectSeat(Seat seat) {
    if (!seat.isAvailable) return;

    final currentPassenger =
        ref.read(passengerInfoProvider)[_currentPassengerIndex];
    final selectedSeats = ref.read(selectedSeatsProvider);

    // Check if seat is already selected by another passenger
    if (selectedSeats.values.contains(seat.id)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('This seat is already selected by another passenger')),
      );
      return;
    }

    // Select the seat
    ref
        .read(selectedSeatsProvider.notifier)
        .selectSeat(currentPassenger.firstName, seat.id);

    // Auto-advance to next passenger if available
    if (_currentPassengerIndex < ref.read(passengerInfoProvider).length - 1) {
      setState(() => _currentPassengerIndex++);
    }
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Seats'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Show only available'),
              value: _showOnlyAvailable,
              onChanged: (value) {
                setState(() => _showOnlyAvailable = value);
                Navigator.of(context).pop();
              },
            ),
            DropdownButtonFormField<SeatClass?>(
              value: _selectedSeatClass,
              decoration: const InputDecoration(labelText: 'Seat Class'),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Classes')),
                ...SeatClass.values.map((seatClass) => DropdownMenuItem(
                      value: seatClass,
                      child: Text(_getSeatClassLabel(seatClass)),
                    )),
              ],
              onChanged: (value) {
                setState(() => _selectedSeatClass = value);
                Navigator.of(context).pop();
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<SeatType?>(
              value: _selectedSeatType,
              decoration: const InputDecoration(labelText: 'Seat Type'),
              items: [
                const DropdownMenuItem(value: null, child: Text('All Types')),
                ...SeatType.values.map((seatType) => DropdownMenuItem(
                      value: seatType,
                      child: Text(_getSeatTypeLabel(seatType)),
                    )),
              ],
              onChanged: (value) {
                setState(() => _selectedSeatType = value);
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _skipSeatSelection() {
    // Clear all selected seats
    ref.read(selectedSeatsProvider.notifier).clear();
    _proceedToBookingConfirmation();
  }

  void _proceedToBookingConfirmation() {
    // TODO: Navigate to FlightBookingConfirmationScreen when implemented
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Seat selection completed! Proceeding to booking confirmation...'),
        backgroundColor: Colors.green,
      ),
    );
    Navigator.of(context).pop();
  }
}
