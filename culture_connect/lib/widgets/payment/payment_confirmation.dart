import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/receipt.dart';
import 'package:culture_connect/services/payment_service.dart';

/// A widget for displaying payment confirmation details
class PaymentConfirmation extends ConsumerStatefulWidget {
  /// The transaction ID
  final String transactionId;

  /// The receipt ID
  final String receiptId;

  /// Callback when the user wants to view the receipt
  final VoidCallback? onViewReceipt;

  /// Callback when the user wants to share the receipt
  final VoidCallback? onShareReceipt;

  /// Creates a new payment confirmation widget
  const PaymentConfirmation({
    super.key,
    required this.transactionId,
    required this.receiptId,
    this.onViewReceipt,
    this.onShareReceipt,
  });

  @override
  ConsumerState<PaymentConfirmation> createState() =>
      _PaymentConfirmationState();
}

class _PaymentConfirmationState extends ConsumerState<PaymentConfirmation> {
  final PaymentService _paymentService = PaymentService();
  Receipt? _receipt;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadReceipt();
  }

  Future<void> _loadReceipt() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final receipt = await _paymentService.getReceipt(widgeteceiptId);
      setState(() {
        _receipt = receipt;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load receipt: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _shareReceipt() async {
    if (widget.onShareReceipt != null) {
      widget.onShareReceipt!();
      return;
    }

    // Receipt sharing functionality has been simplified
    // In a real implementation, this would generate and share a PDF receipt
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Receipt sharing not available in this version')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colorsed,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadReceipt,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_receipt == null) {
      return const Center(
        child: Text('Receipt not found'),
      );
    }

    return Card(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Payment Successful',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Receipt details
            Row(
              children: [
                const Icon(Iconseceipt, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Receipt Number',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  _receipt!eceiptNumber,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Date',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '${_receipt!.createdAt.day}/${_receipt!.createdAt.month}/${_receipt!.createdAt.year}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                const Icon(Icons.payment, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Payment Method',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  _receipt!.paymentMethodName,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                const Icon(Icons.attach_money, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Amount',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  _receipt!.formattedTotalAmount,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Transaction ID
            Row(
              children: [
                const Icon(Icons.numbers, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Transaction ID',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '${widget.transactionId.substring(0, 8)}...',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),

            const Divider(height: 32),

            // Experience details
            Text(
              'Experience Details',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                const Icon(Icons.event_note, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Experience',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  _receipt!.experienceName,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                const Icon(Icons.event, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Date',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  _receipt!.formattedExperienceDate,
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                const Icon(Icons.people, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Participants',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '${_receipt!.participantCount}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignmentaceEvenly,
              children: [
                OutlinedButton.icon(
                  onPressed: widget.onViewReceipt,
                  icon: const Icon(Icons.visibility),
                  label: const Text('View Receipt'),
                ),
                ElevatedButton.icon(
                  onPressed: _shareReceipt,
                  icon: const Icon(Icons.share),
                  label: const Text('Share Receipt'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
